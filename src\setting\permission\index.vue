<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="权限管理"></CustomNavBar>
    </template>
    <view class="setting">
      <view class="setting-list flex-between border-b">
        <view class="list-item-text text-32rpx">位置权限</view>
        <wd-switch
          v-model="checkedStatus[0].checked"
          active-color="#13CE66"
          inactive-color="#777777"
          size="40rpx"
        />
      </view>
      <view class="setting-list flex-between border-b">
        <view class="list-item-text text-32rpx">日历权限</view>
        <wd-switch
          v-model="checkedStatus[1].checked"
          active-color="#13CE66"
          inactive-color="#777777"
          size="40rpx"
        />
      </view>
      <view class="setting-list flex-between border-b">
        <view class="list-item-text text-32rpx">相机权限</view>
        <wd-switch
          v-model="checkedStatus[2].checked"
          active-color="#13CE66"
          inactive-color="#777777"
          size="40rpx"
        />
      </view>
      <view class="setting-list flex-between border-b">
        <view class="list-item-text text-32rpx">麦克风权限</view>
        <wd-switch
          v-model="checkedStatus[3].checked"
          active-color="#13CE66"
          inactive-color="#777777"
          size="40rpx"
        />
      </view>
      <!-- <view class="setting-list flex-between border-b">
        <view class="list-item-text text-32rpx">蓝牙权限</view>
        <wd-switch
          v-model="checkedStatus[4].checked"
          active-color="#13CE66"
          inactive-color="#777777"
          size="40rpx"
        />
      </view> -->
      <view class="setting-list flex-between border-b">
        <view class="list-item-text text-32rpx">相册权限</view>
        <wd-switch
          v-model="checkedStatus[5].checked"
          active-color="#13CE66"
          inactive-color="#777777"
          size="40rpx"
        />
      </view>
      <view class="setting-list flex-between border-b">
        <view class="list-item-text text-32rpx">通知权限</view>
        <wd-switch
          v-model="checkedStatus[6].checked"
          active-color="#13CE66"
          inactive-color="#777777"
          size="40rpx"
        />
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const checkedStatus = ref([
  {
    name: '位置权限',
    checked: true,
  },
  {
    name: '日历权限',
    checked: true,
  },
  {
    name: '相机权限',
    checked: true,
  },
  {
    name: '麦克风权限',
    checked: true,
  },
  // {
  //   name: '蓝牙权限',
  //   checked: false,
  // },
  {
    name: '相册权限',
    checked: true,
  },
  {
    name: '通知权限',
    checked: false,
  },
])
</script>
<style scoped lang="scss">
.setting {
  padding: 0rpx 40rpx;
  .setting-list {
    padding: 40rpx 20rpx;
    .list-item-text {
      color: #333;
    }
  }
}
</style>
