<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '使用记录',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging ref="pagingRef" v-model="pageData" :paging-style="pageStyle" @query="queryList">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          :bordered="false"
          custom-class="px-25rpx"
          left-arrow
          safe-area-inset-top
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">订单中心</text>
          </template>
        </wd-navbar>
      </wd-config-provider>
      <view class="w-400rpx">
        <wd-tabs
          v-model="newsTabsStatus"
          :active-color="'#000'"
          :inactive-color="'#888888'"
          custom-class="custom-class"
          line-height="10rpx"
          line-width="80rpx"
          @change="handleChange"
        >
          <wd-tab
            v-for="(item, index) in newsTabsList"
            :key="item.name"
            :name="index"
            :title="`${item.label}`"
          />
        </wd-tabs>
      </view>
    </template>
    <view class="px-40rpx py-20rpx">
      <template v-for="item in pageData" :key="item.id">
        <orderList :item="item" />
      </template>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { type ConfigProviderThemeVars, useToast, useMessage } from 'wot-design-uni'
import orderList from '@/sub_business/components/orderList.vue'
import { payDealList } from '@/service/order'
import { truncateText, getTimeDifference } from '@/utils/util'
import { DICT_IDS } from '@/enum/diction'
const { getDictData } = useDictionary()
const { pageStyle, pageData, pageInfo, pageSetInfo, pagingRef } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const pollTimer = ref<number | null>(null)
const {
  bool: showPaymentMethod,
  setFalse: setShowPaymentMethod,
  toggle: togglePaymentMethod,
} = useBoolean()
const list = ref({})
interface PaymentMethod {
  icon: string
  name: string
  action: () => void
}

const params = ref({
  entity: {},
  orderBy: {},
  page: pageInfo.page,
  size: pageInfo.size,
})
const queryList = async (page, size) => {
  pageSetInfo(page, size)
  const res = await payDealList({ ...params.value, page: pageInfo.page, size: pageInfo.size })
  if (res.code === 0) {
    res.data.list.forEach((item) => {
      // 确保字典数据已加载，避免 undefined
      item.dealStateName = list.value[item.dealState] || ''
      if (!item.dealState) {
        item.finishTimeD = getTimeDifference(item.finishTime)
      }
    })
    pagingRef.value.complete(res.data.list)
  }
}
const handleChange = ({ index }) => {
  if (index === 0) {
    params.value.entity.dealState = null
  } else if (index === 1) {
    params.value.entity.dealState = 0
  } else if (index === 2) {
    params.value.entity.dealState = 6
  }
  pagingRef.value.reload()
}
// 订单字典
const getDictDataType = async () => {
  const res: any = await getDictData(DICT_IDS.ORDER_PAY_TYPE)
  list.value = res
  console.log(list.value)
}
onLoad(async (options) => {
  await uni.$onLaunched

  // 确保字典数据完全加载完成
  await getDictDataType()

  // 字典数据加载完成后再重新加载分页数据
  await pagingRef.value.reload()
})
// 监听支付成功
uni.$on('paySuccess', () => {
  pagingRef.value.reload()
})

function handleClickLeft() {
  uni.navigateBack()
}

const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
  navbarColor: '#000',
}
const newsTabsStatus = ref(0)
const newsTabsList = ref([
  { name: '1', label: '全部' },
  { name: '2', label: '待付款' },
  { name: '3', label: '已完成' },
])
// 订单字典

onUnload(() => {
  uni.$off('paySuccess')
})
</script>

<style lang="scss" scoped>
:deep(.custom-class) {
  background-color: transparent !important;

  .wd-tabs__nav {
    background-color: transparent;
  }
}

:deep(.wd-tabs__line) {
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
}
</style>
