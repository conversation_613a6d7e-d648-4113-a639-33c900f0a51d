<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging ref="pagingRef" v-model="pageData" :paging-style="pageStyle">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          :bordered="false"
          custom-class="px-25rpx"
          left-arrow
          safe-area-inset-top
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500"></text>
          </template>
        </wd-navbar>
      </wd-config-provider>
    </template>

    <view class="page">
      <view class="invoice-card">
        <view class="invoice-header-bg">
          <view class="invoice-header">
            <text class="invoice-title">开发票</text>
          </view>
          <view class="invoice-body">
            <view class="row type-row">
              <text class="label">
                发票类型
                <text class="star">*</text>
              </text>
              <view class="type-switch">
                <view
                  :class="['type-btn', form.invoiceType === 1 ? 'active' : '']"
                  @click="handleSelectInvoiceType(1)"
                >
                  电子普通发票
                </view>
              </view>
            </view>
            <view class="row type-row">
              <text class="label">
                抬头类型
                <text class="star">*</text>
              </text>
              <view class="type-switch">
                <view
                  :class="['type-btn', form.headUpType === 1 ? 'active' : '']"
                  @click="handleSelectHeadUpType(1)"
                >
                  企业单位
                </view>
                <view
                  :class="['type-btn', form.headUpType === 2 ? 'active' : '']"
                  @click="handleSelectHeadUpType(2)"
                >
                  个人/非企业
                </view>
              </view>
            </view>
            <view class="row">
              <text class="label">
                发票金额
                <text class="star">*</text>
              </text>
              <input
                v-model="form.invoiceMoney"
                class="input value money"
                disabled
                placeholder="请输入金额"
                type="number"
              />
            </view>
            <view v-if="form.headUpType === 1" class="row">
              <text class="label">
                公司抬头
                <text class="star">*</text>
              </text>
              <input
                v-model="form.name"
                :disabled="isDetail"
                class="input value"
                placeholder="请输入公司抬头"
              />
            </view>
            <view v-if="form.headUpType === 2" class="row">
              <text class="label">
                个人抬头
                <text class="star">*</text>
              </text>
              <input
                v-model="form.name"
                :disabled="isDetail"
                class="input value"
                placeholder="请输入个人抬头"
              />
            </view>
            <view v-if="form.headUpType === 1" class="row">
              <text class="label">
                公司税号
                <text class="star">*</text>
              </text>
              <input
                v-model="form.creditCode"
                :disabled="isDetail"
                class="input value"
                placeholder="请输入公司税号"
              />
            </view>
            <!--            <view class="row">-->
            <!--              <text class="label">公司电话</text>-->
            <!--              <input-->
            <!--                v-model="form.companyPhone"-->
            <!--                :disabled="isDetail"-->
            <!--                class="input value"-->
            <!--                placeholder="选填"-->
            <!--              />-->
            <!--            </view>-->
          </view>
        </view>
      </view>
      <view class="other-card">
        <view class="other-card-content">
          <!--          <view class="other-row">-->
          <!--            <text class="label">-->
          <!--              公司地址-->
          <!--              <text class="star">*</text>-->
          <!--            </text>-->
          <!--            <input-->
          <!--              v-model="form.address"-->
          <!--              :disabled="isDetail"-->
          <!--              class="input value"-->
          <!--              placeholder="请输入公司地址"-->
          <!--            />-->
          <!--          </view>-->
          <!--          <view class="other-row">-->
          <!--            <text class="label">-->
          <!--              开户银行-->
          <!--              <text class="star">*</text>-->
          <!--            </text>-->
          <!--            <input-->
          <!--              v-model="form.bankOfDeposit"-->
          <!--              :disabled="isDetail"-->
          <!--              class="input value"-->
          <!--              placeholder="请输入开户银行"-->
          <!--            />-->
          <!--          </view>-->
          <!--          <view class="other-row">-->
          <!--            <text class="label">-->
          <!--              开户账号-->
          <!--              <text class="star">*</text>-->
          <!--            </text>-->
          <!--            <input-->
          <!--              v-model="form.bankCard"-->
          <!--              :disabled="isDetail"-->
          <!--              class="input value"-->
          <!--              placeholder="请输入开户账号"-->
          <!--            />-->
          <!--          </view>-->
          <view class="other-row">
            <text class="label">
              邮箱
              <text class="star">*</text>
            </text>
            <input
              v-model="form.postBox"
              :disabled="isDetail"
              class="input value"
              placeholder="请输入邮箱"
            />
          </view>
          <view class="other-row">
            <text class="label">手机</text>
            <input
              v-model="form.phone"
              :disabled="isDetail"
              class="input value"
              placeholder="请输入手机号码"
            />
          </view>
          <view class="other-row-tip">
            <text class="tip-text">*符号为必填项</text>
          </view>
        </view>
      </view>
      <view v-if="isDetail" class="time-card">
        <text class="label">开票时间</text>
        <text class="time-value">{{ invoiceCreatedTime }}</text>
      </view>
    </view>
  </z-paging>
  <view v-if="!isDetail" class="footer-bar-fixed">
    <view class="footer-bar-inner">
      <view class="footer-info-col">
        <view class="footer-info-row">
          <text class="footer-label">合计:</text>
          <text class="footer-amount">￥ {{ form.invoiceMoney }}</text>
        </view>
        <!-- <view class="footer-record">2笔充值记录</view> -->
      </view>
      <button class="footer-btn-fixed" @click="handleSubmit">开发票</button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { type ConfigProviderThemeVars } from 'wot-design-uni'
import { usePayProp } from '@/sub_business/hooks/usePayProp'
import { addInvoiceRecord, getInvoiceRecordById } from '@/service/walletInvoice'

const invoiceCreatedTime = ref<string>('')
const isDetail = ref<boolean>(false)
const totalAmount = ref<any>(null)
const selectedItems = ref<any>([])

const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
    marginTop: '0rpx',
    marginBottom: isDetail.value ? '200rpx' : '0rpx',
  },
})

const { clearPayPropActive } = usePayProp()
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
}

const form = ref({
  totalList: [
    {
      dealId: null,
      actualAmount: null,
    },
  ],
  dealId: [],
  invoiceType: 1,
  headUpType: 1,
  invoiceMoney: null,
  creditCode: '',
  name: '',
  companyPhone: '',
  address: '',
  bankOfDeposit: '',
  bankCard: '',
  postBox: '',
  phone: '',
})

function handleSelectInvoiceType(type: number) {
  if (isDetail.value) return
  form.value.invoiceType = type
}

function handleSelectHeadUpType(type: number) {
  if (isDetail.value) return
  form.value.headUpType = type
  form.value.name = ''
  form.value.creditCode = ''
}

const handleSubmit = async () => {
  if (!form.value.invoiceMoney) {
    uni.showToast({ title: '请输入发票金额', icon: 'none' })
    return
  }
  if (!form.value.name) {
    if (form.value.headUpType === 1) {
      uni.showToast({ title: '请输入公司抬头', icon: 'none' })
      return
    } else if (form.value.headUpType === 2) {
      uni.showToast({ title: '请输入个人抬头', icon: 'none' })
      return
    }
  }

  if (!form.value.creditCode && form.value.headUpType === 1) {
    uni.showToast({ title: '请输入公司税号', icon: 'none' })
    return
  }
  if (!form.value.postBox) {
    uni.showToast({ title: '请输入邮箱', icon: 'none' })
    return
  }
  // if (!form.value.address) {
  //   uni.showToast({ title: '请输入公司地址', icon: 'none' })
  //   return
  // }
  // if (!form.value.bankOfDeposit) {
  //   uni.showToast({ title: '请输入开户银行', icon: 'none' })
  //   return
  // }
  // if (!form.value.bankCard) {
  //   uni.showToast({ title: '请输入开户账号', icon: 'none' })
  //   return
  // }
  if (!form.value.invoiceType) {
    uni.showToast({ title: '请选择发票类型', icon: 'none' })
    return
  }
  if (!form.value.headUpType) {
    uni.showToast({ title: '请选择抬头类型', icon: 'none' })
    return
  }
  form.value.invoiceMoney = form.value.invoiceMoney && form.value.invoiceMoney * 100
  form.value.totalList = selectedItems.value.map((item) => ({
    dealId: item.id,
    actualAmount: item.amount,
  }))
  form.value.dealId = selectedItems.value.map((item) => item.id)
  console.log('提交的表单数据:', form.value)
  const res: any = await addInvoiceRecord({ ...form.value }).then((res: any) => {
    if (res.code === 0) {
      uni.showToast({ title: '提交成功', icon: 'success' })
      isDetail.value = false
      uni.navigateBack()
    } else {
      uni.showToast({ title: res.msg, icon: 'none' })
    }
  })
}

const getInvoiceRecordData = async (id: any) => {
  const res: any = await getInvoiceRecordById({ id })
  if (res.code === 0) {
    res.data.invoiceMoney = (res.data.invoiceMoney / 100).toFixed(2)
    invoiceCreatedTime.value = res.data.createTime
    form.value = res.data
  }
}

function handleClickLeft() {
  uni.navigateBack()
}

onLoad((options: any) => {
  if (options.id) {
    isDetail.value = true
    getInvoiceRecordData(options.id)
  } else {
    const invoiceData = uni.getStorageSync('invoiceSelectedData')
    if (invoiceData) {
      totalAmount.value = invoiceData.totalAmount
      selectedItems.value = invoiceData.selectedItems
      form.value.invoiceMoney = invoiceData.totalAmount

      uni.removeStorageSync('invoiceSelectedData')

      console.log('获取到的发票数据:', invoiceData)
    }
  }
  pagingRef.value?.reload()
})
onMounted(async () => {
  pagingRef.value?.reload()
})

onBeforeUnmount(() => {
  clearPayPropActive()
})
</script>

<style lang="scss" scoped>
.invoice-card {
  margin: 0 24rpx 32rpx 24rpx;
  background: transparent;
  border: 2rpx dashed #a4a4a4;
  border-radius: 30rpx;
}

.invoice-header-bg {
  min-width: 618rpx;
  min-height: 614rpx;
  margin: 10rpx 14rpx;
  background-image: url('@/static/mine/business/Invoicing_bg.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border-radius: 28rpx;
}

.invoice-header {
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 96rpx;
  border-top-left-radius: 28rpx;
  border-top-right-radius: 28rpx;
  box-shadow: 0 4rpx 16rpx 0 rgba(95, 136, 255, 0.12);
}

.invoice-title {
  margin-bottom: -7rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}

.invoice-body {
  min-height: 650rpx;
  padding: 86rpx 28rpx 28rpx 28rpx;
  background: transparent;
}

.row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28rpx 0;
  font-size: 24rpx;
  border-bottom: 1rpx solid #e1e1e1;
}

.label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.star {
  margin-left: 4rpx;
  color: #ff4d4f;
}

.value {
  font-size: 28rpx;
  color: #222;
  text-align: right;
}

.money {
  font-weight: 600;
  color: #1a73e8;
}

.placeholder {
  color: #bbb;
}

.type-switch {
  display: flex;
  gap: 12rpx;
}

.type-btn {
  padding: 8rpx 28rpx;
  font-size: 24rpx;
  font-weight: normal;
  color: #000000;
  background: #f3f3f3;
  border: 2rpx solid transparent;
  border-radius: 10rpx;
  transition: all 0.2s;
}

.type-btn.active {
  color: #326fff;
  background: #e6f0ff;
  border-color: #6fa5ff;
}

.input {
  min-width: 420rpx;
  height: 44rpx;
  padding: 0;
  font-size: 28rpx;
  color: #222;
  text-align: right;
  background: transparent;
  border: none;
  outline: none;
}

.input::placeholder {
  font-size: 26rpx;
  color: #bbb;
}

.other-card {
  min-height: 400rpx;
  margin: 32rpx 24rpx 0 24rpx;
  overflow: hidden;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
}

.other-card-content {
  padding: 45rpx;
}

.other-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72rpx;
  padding: 56rpx 0;
  font-size: 24rpx;
  color: #000000;
  border-bottom: 1rpx solid #e1e1e1;
}

.other-row-tip {
  margin-top: 10rpx;

  .tip-text {
    font-size: 26rpx;
    color: #888;
  }
}

// .other-row:last-child {
//   border-bottom: none;
// }
.tip-row {
  justify-content: flex-end;
  height: 40rpx;
  border-bottom: none;
}

.tip {
  margin-top: 2rpx;
  font-size: 22rpx;
  color: #bbb;
}

.time-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 130rpx;
  padding: 0 24rpx;
  margin: 28rpx 24rpx;
  font-size: 28rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.06);
}

.time-value {
  font-weight: 500;
  color: #222;
}

.footer-bar {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding: 0 24rpx;
  margin: 36rpx 0 0 0;
}

.footer-left {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: flex-start;
}

.footer-label {
  font-size: 28rpx;
  color: #222;
}

.footer-amount {
  margin-left: 4rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a73e8;
}

.footer-record {
  margin-top: 4rpx;
  font-size: 22rpx;
  color: #bbb;
}

.footer-btn {
  width: 180rpx;
  height: 72rpx;
  margin-left: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #fff;
  background: linear-gradient(90deg, #6fa5ff 0%, #5d7cff 100%);
  border: none;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx 0 rgba(95, 136, 255, 0.12);
}

.footer-bar-fixed {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99;
  padding: 0 24rpx 24rpx 24rpx;
  pointer-events: none;
  background: transparent;
}

.footer-bar-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx 24rpx 32rpx;
  pointer-events: auto;
  background: #fafbfc;
  border-radius: 24rpx;
  box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
}

.footer-info-col {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: flex-start;
}

.footer-info-row {
  display: flex;
  align-items: baseline;
  font-size: 32rpx;
}

.footer-label {
  margin-right: 4rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #111;
}

.footer-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #1976ed;
}

.footer-record {
  margin-top: 8rpx;
  margin-left: 2rpx;
  font-size: 24rpx;
  color: #bbb;
}

.footer-btn-fixed {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 172rpx;
  height: 96rpx;
  font-size: 32rpx;
  color: #fff;
  background-color: #1677ff;
  border-radius: 24rpx;
}
</style>
