import { POST } from '../index'
import { cancelDealStatusInt, payDealDetailInt, payDealListInt, payDealStatusInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** 订单信息 */
export const payDealList = (data: payDealListInt, config?: HttpRequestConfig) =>
  POST<any>('/easyzhipin-api/payDeal/queryAllList', data, config)
// 订单详情
export const payDealDetail = (data: payDealDetailInt, config?: HttpRequestConfig) =>
  POST<any>('/easyzhipin-api/payDeal/detail', data, config)

// 订单支付状态
export const payDealStatus = (data: payDealStatusInt, config?: HttpRequestConfig) =>
  POST<any>('/easyzhipin-api/pay/queryPayParam', data, config)

// 取消订单
export const cancelDealStatus = (data: cancelDealStatusInt, config?: HttpRequestConfig) =>
  POST<any>('/easyzhipin-api/payDeal/cancelPay', data, config)

//可开发票订单列表
export const invoiceOrderList = (data: payDealListInt, config?: HttpRequestConfig) =>
  POST<any>('/easyzhipin-api/hrInvoiceRecord/queryDealList', data, config)
