import { payPrePay } from '@/service/pay'
import type { payPrePayDataInt } from '@/service/pay/types'

interface PayPrePayParams extends payPrePayDataInt {
  provider: 'alipay' | 'wxpay'
}

/** 支付hooks */
export const usePayment = () => {
  /** 支付函数 */
  const paymentPrePay = (params: PayPrePayParams) => {
    return new Promise<string>((resolve, reject) => {
      const executePayment = async () => {
        try {
          const { provider, ...payParams } = params
          const isWx = payParams.payPass === 0
          const { data } = await payPrePay(payParams)
          const { outTradeNo, ...orderInfo } = data
          uni.requestPayment({
            provider,
            orderInfo: isWx ? orderInfo : orderInfo.orderStr,
            success: () => {
              resolve(outTradeNo)
            },
            fail: (err) => {
              reject(err)
            },
          })
        } catch (error) {
          reject(error)
        }
      }
      executePayment()
    })
  }
  // 详情支付
  return {
    paymentPrePay,
  }
}
