// manifest.config.ts
import { defineManifestConfig } from '@uni-helper/vite-plugin-uni-manifest'
import path from 'node:path'
import { loadEnv } from 'vite'

// 获取环境变量的范例
const env = loadEnv(process.env.VITE_NODE_ENV!, path.resolve(process.cwd(), 'env'))
const {
  VITE_APP_TITLE,
  VITE_UNI_APPID,
  VITE_WX_APPID,
  VITE_APP_PUBLIC_BASE,
  VITE_FALLBACK_LOCALE,
} = env

export default defineManifestConfig({
  name: VITE_APP_TITLE,
  appid: VITE_UNI_APPID,
  description: '',
  versionName: '1.0.0',
  versionCode: '109',
  transformPx: false,
  locale: VITE_FALLBACK_LOCALE, // 'zh-Hans'
  h5: {
    router: {
      base: VITE_APP_PUBLIC_BASE,
    },
  },
  /* 5+App特有相关 */
  'app-plus': {
    usingComponents: true,
    nvueStyleCompiler: 'uni-app',
    compilerVersion: 3,
    compatible: {
      ignoreVersion: true,
    },
    splashscreen: {
      alwaysShowBeforeRender: false,
      waiting: false,
      autoclose: false,
    },
    nativePlugins: {
      'pdf-viewer': {
        __plugin_info__: {
          name: 'pdf-viewer',
          description: 'App端 PDF 文件原生预览组件，仅支持本地文件路径',
          platforms: 'Android,iOS',
          url: '',
          android_package_name: '',
          ios_bundle_id: '',
          isCloud: false,
          bought: -1,
          pid: '',
          parameters: {},
        },
      },
      'AliCloud-NirvanaPns': {
        __plugin_info__: {
          name: '阿里云号码认证SDK',
          description: '阿里云号码认证SDK，包含一键登录和本机号码校验两个功能。',
          platforms: 'Android,iOS',
          url: 'https://ext.dcloud.net.cn/plugin?id=4297',
          android_package_name: 'com.easyzhipin.easyzhipinapp',
          ios_bundle_id: 'com.easyzhipin.easyzhipinapp',
          isCloud: true,
          bought: 1,
          pid: '4297',
          parameters: {},
        },
      },
    },
    /* 模块配置 */
    modules: {
      Camera: {},
      Geolocation: {},
      Push: {},
      Payment: {},
    },
    /* 应用发布信息 */
    distribute: {
      /* android打包配置 */
      android: {
        minSdkVersion: 28,
        targetSdkVersion: 34,
        // minSdkVersion: 34,
        // targetSdkVersion: 34,
        abiFilters: ['arm64-v8a', 'x86'],
        // abiFilters: ['arm64-v8a', 'x86'],
        // Android 隐私配置
        privacy: {
          // 启用隐私弹窗
          enable: true,
          // 隐私配置文件路径（相对于项目根目录）
          configPath: 'src/androidPrivacy.json',
        },
        permissions: [
          '<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>',
          '<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>',
          '<uses-permission android:name="android.permission.CAMERA"/>',
          '<uses-feature android:name="android.hardware.camera"/>',
          '<uses-feature android:name="android.hardware.camera.autofocus"/>',
          '<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>',
          '<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>',
          '<uses-permission android:name="android.permission.INTERNET"/>',
          '<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>',
          '<uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>',
          '<uses-permission android:name="android.permission.VIBRATE"/>',
          '<uses-permission android:name="android.permission.WAKE_LOCK"/>',
          '<uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>',
          '<uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>',
          '<uses-permission android:name="android.permission.READ_MEDIA_AUDIO"/>',
          '<uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />',
          '<uses-permission android:name="android.permission.READ_PHONE_STATE"/>',
          '<uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>',
        ],
      },
      /* ios打包配置 */
      ios: {
        pushRegisterMode: 'manual',
        dSYMs: false,
        idfa: false,
        privacyDescription: {
          NSLocationWhenInUseUsageDescription:
            '用于推荐附近岗位，辅助查看企业位置、规划路线，匹配通勤合适的工作，助力提升求职效率用于推荐附近岗位，辅助查看企业位置、规划路线，匹配通勤合适的工作，助力提升求职效率',
          NSPhotoLibraryUsageDescription: '支持您上传头像、证书等材料，完成资质核验，加快招聘对接',
          NSPhotoLibraryAddUsageDescription:
            '仅用于您主动保存图片，不会擅自操作相册内容，全力保障您的相册隐私安全',
          NSCameraUsageDescription: ' 为帮您完善求职资料，如拍摄头像等，我们需申请相机使用权限',
          NSMicrophoneUsageDescription:
            '希望获取麦克风权限，以支持音视频录制等功能，保障相关内容顺利创作记录',
          NSLocationAlwaysUsageDescription:
            '为方便找到周边合适工作，需持续获取地理位置，以此推荐附近岗位，提升求职效率',
          NSLocationAlwaysAndWhenInUseUsageDescription:
            ' 为推荐附近岗位需位置权限，使用时匹配岗位，访问可查看通勤路线',
        },
      },
      // /* SDK配置 */
      sdkConfigs: {
        geolocation: {
          system: {
            __platform__: ['ios', 'android'],
          },
        },
        maps: {},
        payment: {
          weixin: {
            __platform__: ['android'],
            appid: 'wxf1c1b8079cda01c0',
          },
          alipay: {
            __platform__: ['android'],
          },
        },
      },
      /* 图标配置 */
      icons: {
        android: {
          hdpi: 'src/static/app/icons/72x72.png',
          xhdpi: 'src/static/app/icons/96x96.png',
          xxhdpi: 'src/static/app/icons/144x144.png',
          xxxhdpi: 'src/static/app/icons/192x192.png',
        },
        ios: {
          appstore: 'src/static/app/icons/1024x1024.png',
          ipad: {
            app: 'src/static/app/icons/76x76.png',
            'app@2x': 'src/static/app/icons/152x152.png',
            notification: 'src/static/app/icons/20x20.png',
            'notification@2x': 'src/static/app/icons/40x40.png',
            'proapp@2x': 'src/static/app/icons/167x167.png',
            settings: 'src/static/app/icons/29x29.png',
            'settings@2x': 'src/static/app/icons/58x58.png',
            spotlight: 'src/static/app/icons/40x40.png',
            'spotlight@2x': 'src/static/app/icons/80x80.png',
          },
          iphone: {
            'app@2x': 'src/static/app/icons/120x120.png',
            'app@3x': 'src/static/app/icons/180x180.png',
            'notification@2x': 'src/static/app/icons/40x40.png',
            'notification@3x': 'src/static/app/icons/60x60.png',
            'settings@2x': 'src/static/app/icons/58x58.png',
            'settings@3x': 'src/static/app/icons/87x87.png',
            'spotlight@2x': 'src/static/app/icons/80x80.png',
            'spotlight@3x': 'src/static/app/icons/120x120.png',
          },
        },
      },
      splashscreen: {
        androidStyle: 'default',
        android: {
          hdpi: 'src/static/app/hdpi/480.9.png',
          xhdpi: 'src/static/app/hdpi/720.9.png',
          xxhdpi: 'src/static/app/hdpi/1080.9.png',
        },
      },
    },
  },
  /* 快应用特有相关 */
  quickapp: {},
  /* 小程序特有相关 */
  'mp-weixin': {
    appid: VITE_WX_APPID,
    setting: {
      urlCheck: false,
    },
    usingComponents: true,
    requiredPrivateInfos: ['getLocation', 'chooseLocation'],
    permission: {
      'scope.userLocation': {
        desc: '您的位置信息将用于小程序位置接口的效果展示',
      },
    },
    // __usePrivacyCheck__: true,
  },
  'mp-alipay': {
    usingComponents: true,
    styleIsolation: 'shared',
  },
  'mp-baidu': {
    usingComponents: true,
  },
  'mp-toutiao': {
    usingComponents: true,
  },
  uniStatistics: {
    enable: false,
  },
  vueVersion: '3',
})
