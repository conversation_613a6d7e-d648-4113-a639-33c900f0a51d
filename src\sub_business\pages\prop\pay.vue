<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging
    :paging-style="pageStyle"
    bottom-bg-color="#ffffff"
    layout-only
    paging-class="pay-paging"
    safe-area-inset-bottom
  >
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          :bordered="false"
          custom-class="!bg-transparent"
          fixed
          left-arrow
          safe-area-inset-top
          @click-left="handleBack"
          @click-right="handleGoCdk"
        >
          <template v-if="!sourcePosition" #right>
            <text class="c-white text-28rpx">兑换</text>
          </template>
        </wd-navbar>
        <view
          :style="{ backgroundImage: `url(${payBgImage})` }"
          class="h-400rpx relative bg-cover bg-center bg-no-repeat"
        >
          <view class="flex flex-col absolute left-48rpx bottom-50rpx gap-22rpx">
            <wd-img
              :height="payPropCardInfo.style.height"
              :src="payPropCardInfo.title"
              :width="payPropCardInfo.style.width"
            />
            <view
              class="w-440rpx h-76rpx bg-#2F2F2F rounded-full flex items-center pr-34rpx gap-10rpx"
              @click="selectPostPopupShowBoolTrue"
            >
              <view class="flex items-center gap-20rpx flex-1">
                <wd-img
                  :src="defaultRoleLogo(userIntel.gender, userIntel.headImgUrl)"
                  height="70rpx"
                  round
                  width="70rpx"
                />
                <text class="c-#E4CC9C text-28rpx line-clamp-1 flex-1">
                  {{ releasePropPostActivePost.positionName }}
                </text>
              </view>
              <text
                :class="
                  selectPostPopupShowBool
                    ? 'i-carbon-triangle-solid'
                    : 'i-carbon-triangle-down-solid'
                "
                class="text-16rpx c-#E4CC9C"
              />
            </view>
          </view>
        </view>
      </wd-config-provider>
    </template>
    <wd-config-provider v-if="sourcePosition" :themeVars="themeVars">
      <view class="center">
        <view class="w-400rpx">
          <wd-tabs
            v-model="payPropTypeActive"
            color="#A28064"
            inactive-color="#9E9E9E"
            line-height="4rpx"
            line-width="134rpx"
          >
            <wd-tab
              v-for="(item, key) in payPropTypeList"
              :key="`tab-${key}`"
              :name="key"
              :title="`${item.name}`"
            />
          </wd-tabs>
        </view>
      </view>
    </wd-config-provider>
    <scroll-view class="whitespace-nowrap" scroll-x>
      <view class="flex flex-row gap-40rpx px-32rpx py-50rpx">
        <view
          v-for="(item, key) in filteredPayPropList"
          :key="`prop-${key}`"
          :class="{
            '!border-#A28064': selectedPayProp?.id === item.id,
          }"
          :style="{ width: 'calc((100vw - 144rpx) / 3)' }"
          class="relative flex flex-col items-center bg-white border-5rpx border-solid border-#e5e5e5 rounded-32rpx pb-62rpx flex-shrink-0"
          @click="selectedPayProp = item"
        >
          <view
            :class="{
              '!bg-#A28064': selectedPayProp?.id === item.id,
            }"
            class="center w-70% h-38rpx text-22rpx text-white bg-#dcdada mt--1px"
            style="clip-path: polygon(0% 0%, 100% 0%, 80% 100%, 20% 100%)"
          >
            {{ sourcePosition ? item.categoryName : formatDuration(item) }}
          </view>
          <view class="text-#9E9E9E text-28rpx mt-20rpx relative line-through">
            ￥{{ divide(item.originalPrice, 100) }}
          </view>
          <view class="mt-24rpx">
            <text
              :class="{
                'text-#A28064': selectedPayProp?.id === item.id,
              }"
              class="c-#9E9E9E text-26rpx"
            >
              ￥
            </text>
            <text
              :class="{
                'text-#A28064': selectedPayProp?.id === item.id,
              }"
              class="text-36rpx font-500 text-#333333"
            >
              {{ divide(item.price, 100) }}
            </text>
          </view>
        </view>
      </view>
    </scroll-view>
    <view class="px-32rpx pb-32rpx">
      <text class="whitespace-pre-wrap c-#555555 text-28rpx">
        {{ selectedPayProp?.categoryDesc }}
      </text>
    </view>
    <template #bottom>
      <wd-skeleton
        :loading="skeletonLoading"
        :row-col="[[{ width: '100%', height: '168rpx' }]]"
        animation="flashed"
      >
        <view
          class="bg-white shadow-[0rpx_8rpx_20rpx_0rpx_rgba(0,0,0,0.3)] px-24rpx pt-28rpx pb-38rpx flex items-center gap-40rpx"
        >
          <view class="c-#333333">
            <text class="text-28rpx">￥</text>
            <text class="text-56rpx">{{ divide(selectedPayProp?.price ?? 0, 100) }}</text>
          </view>
          <view
            class="rounded-16rpx bg-#2F2F2F h-112rpx w-full flex-1 relative flex items-center pl-46rpx"
          >
            <view
              :class="{ show: showPaymentMethod }"
              class="absolute bottom-130rpx w-306rpx bg-white rounded-16rpx shadow-lg z-10 payment-popup overflow-hidden"
            >
              <view class="flex flex-col gap-24rpx px-30rpx py-26rpx">
                <view
                  v-for="(paymentItem, index) in paymentMethods"
                  :key="`payment-${index}`"
                  class="flex items-center gap-26rpx"
                  @click="paymentItem.action"
                >
                  <wd-icon :name="paymentItem.icon" size="50rpx" />
                  <text class="text-28rpx c-#333333">{{ paymentItem.name }}</text>
                </view>
              </view>
            </view>
            <view class="flex items-center gap-38rpx w-full" @click="togglePaymentMethod">
              <view class="flex items-center c-#E4CC9C text-34rpx gap-20rpx">
                <text>¥{{ divide(selectedPayProp?.price ?? 0, 100) }}</text>
                <text class="text-28rpx">购买{{ selectedPayProp?.categoryName }}</text>
              </view>
              <view class="h-70rpx center border-l-1px border-l-solid border-l-#575757 flex-1">
                <wd-icon color="#E4CC9C" name="arrow-up" size="30rpx" />
              </view>
            </view>
          </view>
        </view>
      </wd-skeleton>
    </template>
  </z-paging>
  <select-post v-model:show="selectPostPopupShowBool" @select-post="handleSelectPropPost" />
</template>

<script lang="ts" setup>
import { useToast, useMessage, CommonUtil } from 'wot-design-uni'
import { PropCardType, PayPropId, EMIT_EVENT, USER_TYPE } from '@/enum'
import { PAY_PROP_TYPE_CONFIG } from '@/enum/payProp'
import { divide } from '@/utils'
import { useReleasePost as useReleaseHomePost } from '@/hooks/business/useReleasePost'
import { useReleasePost } from '@/sub_business/hooks/useReleasePost'
import { usePayProp } from '@/sub_business/hooks/usePayProp'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import { payPropQueryPropCategoryListByPosition } from '@/service/payProp'
import { payQueryDealStatus } from '@/service/pay'
import { hrPositionQueryOptionList } from '@/service/hrPosition'
import type { hrPositionQueryOptionListInt } from '@/service/hrPosition/types'
import {
  type payPropQueryPropCategoryListByPositionInt,
  DurationType,
} from '@/service/payProp/types'
import selectPost from '@/components/common/select-post.vue'
import payBgImage from '@/sub_business/static/prop/pay-bg.png'
import aliPay from '@/sub_business/static/prop/ali-pay.png'
import wechatPay from '@/sub_business/static/prop/wechat-pay.png'
import speedTopPayTitle from '@/sub_business/static/prop/speed-top-pay-title.png'
import magicBombPayTitle from '@/sub_business/static/prop/magic-bomb-pay-title.png'
import exposureRefreshPayTitle from '@/sub_business/static/prop/exposure-refresh-pay-title.png'
import postJobPosition from '@/sub_business/static/prop/post-job-position.png'

interface PayPropType {
  id: readonly PayPropId[]
  name: string
}

interface PaymentMethod {
  icon: string
  name: string
  action: () => void
}

const DEFAULT_ID_MAP = { 0: 18, 1: 22 } as const
const toast = useToast()
const message = useMessage()
const { defaultRoleLogo } = useDefault({
  roleType: USER_TYPE.HR,
})
const { userIntel } = useUserInfo()
const { payPropActive } = usePayProp()
const { paymentPrePay } = usePayment()
const { pageParams } = usePagePeriod()
const { resetReleasePostModel } = useReleasePost()
const { releasePropPostActivePost } = useReleaseHomePost()
const { pageStyle } = usePaging({
  style: {
    background: '#464646',
  },
})
const {
  bool: showPaymentMethod,
  setFalse: setShowPaymentMethod,
  toggle: togglePaymentMethod,
} = useBoolean()
const {
  bool: selectPostPopupShowBool,
  setTrue: selectPostPopupShowBoolTrue,
  setFalse: selectPostPopupShowBoolFalse,
} = useBoolean()
const {
  bool: skeletonLoading,
  setTrue: setSkeletonLoadingTrue,
  setFalse: setSkeletonLoadingFalse,
} = useBoolean(true)
const themeVars: ConfigProviderThemeVars = {
  navbarColor: '#ffffff',
  tabsNavLineBgColor: '#A28064',
}
const PropCardList = {
  [PropCardType.SPEED_TOP]: {
    title: speedTopPayTitle,
    style: {
      height: '70rpx',
      width: '260rpx',
    },
  },
  [PropCardType.MAGIC_BOMB]: {
    title: magicBombPayTitle,
    style: {
      height: '66rpx',
      width: '422rpx',
    },
  },
  [PropCardType.EXPOSURE_REFRESH]: {
    title: exposureRefreshPayTitle,
    style: {
      height: '70rpx',
      width: '430rpx',
    },
  },
  [PropCardType.RELEASE_POSITION]: {
    title: postJobPosition,
    style: {
      height: '84rpx',
      width: '318rpx',
    },
  },
} as const
const pollTimer = ref<number | null>(null)
const selectedPropId = ref<number | null>(null)
const payPropTypeActive = ref(0)
const payPropTypeList = ref<PayPropType[]>([
  {
    id: PAY_PROP_TYPE_CONFIG.BASIC.ids,
    name: PAY_PROP_TYPE_CONFIG.BASIC.name,
  },
  {
    id: PAY_PROP_TYPE_CONFIG.ADVANCED.ids,
    name: PAY_PROP_TYPE_CONFIG.ADVANCED.name,
  },
])
const payPropList = ref<payPropQueryPropCategoryListByPositionInt[]>([])
const sourcePosition = computed(() => !!pageParams.value?.positionId)
const paramsPositionId = computed(
  () => pageParams.value?.positionId ?? releasePropPostActivePost.value.id,
)
const payPropId = computed(() => payPropActive.value || PropCardType.RELEASE_POSITION)
const payPropCardInfo = computed(() => PropCardList[payPropId.value as keyof typeof PropCardList])
const filteredPayPropList = computed(() => {
  if (!sourcePosition.value) {
    return payPropList.value
  }
  const currentType = payPropTypeList.value[payPropTypeActive.value]
  if (!currentType) return []
  return payPropList.value.filter((item) => currentType.id.includes(item.id))
})
const selectedPayProp = computed({
  get() {
    const filtered = filteredPayPropList.value
    if (filtered.length === 0) return null
    let selectedItem: payPropQueryPropCategoryListByPositionInt | undefined
    if (selectedPropId.value) {
      selectedItem = filtered.find((item) => item.id === selectedPropId.value)
      if (selectedItem) return selectedItem
    }
    const defaultId = DEFAULT_ID_MAP[payPropTypeActive.value as keyof typeof DEFAULT_ID_MAP]
    if (defaultId) {
      const defaultItem = filtered.find((item) => item.id === defaultId)
      if (defaultItem) {
        return defaultItem
      }
    }
    return filtered[0] || null
  },
  set(value: payPropQueryPropCategoryListByPositionInt | null) {
    selectedPropId.value = value?.id || null
  },
})

/**
 * 格式化时间显示
 * @param item payProp数据项
 * @returns 格式化后的时间字符串，如 "2小时"、"1日"
 */
const formatDuration = (item: payPropQueryPropCategoryListByPositionInt): string => {
  if (!item.durationTime || item.durationType === undefined) {
    return ''
  }
  const durationMap = {
    [DurationType.HOUR]: '小时',
    [DurationType.DAY]: '日',
    [DurationType.MONTH]: '月',
    [DurationType.YEAR]: '年',
  }
  const unit = durationMap[item.durationType] || ''
  return `${item.durationTime}${unit}`
}

async function fetchHrPositionQueryOptionList() {
  const { data } = await hrPositionQueryOptionList({
    page: 1,
    size: 1,
  })
  const { list } = data
  const [first] = list
  releasePropPostActivePost.value = first ?? {}
  await payPropQueryPropCategoryListByPositionApi()
}

async function handleSelectPropPost(post: hrPositionQueryOptionListInt) {
  releasePropPostActivePost.value = post
  selectPostPopupShowBoolFalse()
}

async function payPropQueryPropCategoryListByPositionApi() {
  if (!paramsPositionId.value || !payPropId.value) return
  setSkeletonLoadingTrue()
  const { data } = await payPropQueryPropCategoryListByPosition({
    positionId: paramsPositionId.value,
    propId: payPropId.value,
  })
  payPropList.value = data
  setSkeletonLoadingFalse()
}

function handleGoCdk() {
  uni.navigateTo({
    url: CommonUtil.buildUrlWithParams(`/sub_business/pages/prop/cdk`, {
      params: JSON.stringify({
        positionId: paramsPositionId.value,
        propId: payPropId.value,
        propCategoryId: selectedPropId.value,
      }),
    }),
  })
}

const paymentMethods = ref<PaymentMethod[]>([
  {
    icon: aliPay,
    name: '支付宝',
    action: () => handlePayment('alipay'),
  },
  {
    icon: wechatPay,
    name: '微信',
    action: () => handlePayment('wxpay'),
  },
])

const handlePayment = async (provider: 'alipay' | 'wxpay') => {
  setShowPaymentMethod()
  const payPass = provider === 'wxpay' ? 0 : 1
  toast.loading({
    msg: '支付中...',
    loadingType: 'outline',
    duration: 0,
    cover: true,
  })
  try {
    const outTradeNo = await paymentPrePay({
      provider,
      payPass,
      positionInfoId: paramsPositionId.value,
      propType: 0,
      propId: payPropId.value,
      propCategoryId: selectedPropId.value,
    })
    // 开始轮询支付状态
    await pollPaymentStatus(outTradeNo)
  } catch (error) {
    toast.close()
  }
}

/**
 * 轮询支付状态
 * @param outTradeNo 订单号
 */
const pollPaymentStatus = async (outTradeNo: string) => {
  const maxAttempts = 60
  const interval = 2000
  let attempts = 0
  const checkStatus = async (): Promise<void> => {
    attempts++
    try {
      const { data } = await payQueryDealStatus({
        outTradeNo,
      })
      if (data) {
        clearPollTimer()
        toast.close()
        if (sourcePosition.value) {
          toast.show('发布成功')
          setTimeout(() => {
            uni.$emit(EMIT_EVENT.REFRESH_PUBLISH_POSITION)
            uni.redirectTo({ url: '/sub_business/pages/positionManage/index' })
            resetReleasePostModel()
          }, 1500)
          return
        }
        toast.show('购买成功')
        setTimeout(() => {
          uni.$emit(EMIT_EVENT.REFRESH_PROP)
          uni.navigateBack()
        }, 1500)
        return
      }
      if (attempts < maxAttempts) {
        console.log('attempts')
      } else {
        clearPollTimer()
        toast.close()
        message
          .alert({
            title: '提示',
            msg: '支付状态确认超时，请到订单页面查看支付结果',
            closeOnClickModal: false,
            confirmButtonText: '知道了',
          })
          .then(() => {
            // TODO: 跳转到订单页面
          })
      }
    } catch (error) {
      if (attempts < maxAttempts) {
        pollTimer.value = setTimeout(() => checkStatus(), interval)
      } else {
        clearPollTimer()
        toast.close()
        toast.error({
          msg: '网络异常',
          duration: 2000,
        })
      }
    }
  }
  pollTimer.value = setTimeout(() => checkStatus(), 1000)
}

/**
 * 清除轮询定时器
 */
const clearPollTimer = () => {
  if (pollTimer.value) {
    clearTimeout(pollTimer.value)
    pollTimer.value = null
  }
}

const handleBack = () => {
  uni.navigateBack()
}

async function reload() {
  if (!sourcePosition.value) {
    await fetchHrPositionQueryOptionList()
  } else {
    await payPropQueryPropCategoryListByPositionApi()
  }
}

watch(
  selectedPayProp,
  (newValue) => {
    if (newValue?.id && selectedPropId.value !== newValue.id) {
      selectedPropId.value = newValue.id
    }
  },
  { immediate: true },
)

watch(payPropTypeActive, () => {
  selectedPropId.value = null
})
onMounted(async () => {
  await uni.$onLaunched
  await reload()
})

onBeforeUnmount(() => {
  clearPollTimer()
})
</script>

<style lang="scss" scoped>
.pay-paging {
  :deep(.zp-view-super) {
    height: 100%;
    background: #ffffff;
    border-radius: 20rpx 20rpx 0 0;
  }
}

:deep(.wd-tabs) {
  background: transparent;

  .wd-tabs__line {
    bottom: 8px;
  }
}

.payment-popup {
  max-height: 0;
  transition: max-height 0.3s ease-in;
  transform-origin: bottom;

  &.show {
    max-height: 300rpx;
  }
}
</style>
