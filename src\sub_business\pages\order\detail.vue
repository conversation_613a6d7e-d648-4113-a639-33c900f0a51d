<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '详情',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging :paging-style="pageStyle" ref="pagingRef" layout-only>
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          left-arrow
          :bordered="false"
          safe-area-inset-top
          custom-class="px-25rpx"
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">详情</text>
          </template>
        </wd-navbar>
      </wd-config-provider>
      <view class="w-580rpx"></view>
    </template>
    <view class="px-40rpx p-b-40rpx p-t-20rpx">
      <view
        class="shadow-[0px_4px_13.5px_0px_rgba(0,0,0,0.15)] py-30rpx bg-white rounded-20rpx relative m-b-10rpx"
      >
        <view
          class="relative px-40rpx p-b-30rpx border-b-1px border-b-solid border-b-[#D8D8D8] m-b-20rpx"
        >
          <view class="flex items-center justify-between">
            <view class="text-36rpx font500 c-#333 p-b-6rpx">{{ pageData.goodsDesc }}</view>
            <wd-count-down
              v-if="!pageData.dealState && pageData.finishTimeD > 0"
              :time="pageData.finishTime"
              format="mm:ss"
            />
          </view>

          <view class="flex items-center gap-10rpx">
            <view class="text-28rpx c-#333">{{ pageData.positionName }}</view>
            <view class="text-32rpx c-#333">￥{{ pageData.actualAmount / 100 }}</view>
          </view>
          <view
            class="absolute top-30rpx right-0 p-r-40rpx text-32rpx c-#333"
            v-if="pageData.dealState"
          >
            {{ pageData.dealStateName }}
          </view>
        </view>
        <view class="px-40rpx p-b-30rpx" v-if="pageData.totalAmount">
          <view class="flex items-center justify-between">
            <view class="text-28rpx c-#333">价格</view>
            <view class="text-32rpx c-#333">￥{{ pageData.totalAmount / 100 }}元</view>
          </view>
        </view>
        <view class="px-40rpx p-b-30rpx" v-if="pageData.discountAmount">
          <view class="flex items-center justify-between">
            <view class="text-28rpx c-#333">优惠</view>
            <view class="text-32rpx c-#333">{{ pageData.discountAmount / 100 }}元</view>
          </view>
        </view>
        <view class="px-40rpx p-b-30rpx" v-if="pageData.actualAmount">
          <view class="flex items-center justify-between">
            <view class="text-28rpx c-#333">付款金额</view>
            <view class="text-32rpx c-#333">￥{{ pageData.actualAmount / 100 }}元</view>
          </view>
        </view>

        <view class="px-40rpx p-t-30rpx border-t-1px border-t-solid border-t-[#D8D8D8] m-t-10rpx">
          <view class="flex items-center justify-between">
            <view class="text-28rpx c-#333">订单编号</view>
            <view class="text-28rpx c-#333">{{ pageData.outTradeNo }}</view>
          </view>
        </view>
        <view class="px-40rpx p-t-30rpx" v-if="pageData.dealCreateTime">
          <view class="flex items-center justify-between">
            <view class="text-28rpx c-#333">下单时间</view>
            <view class="text-28rpx c-#333">{{ pageData.dealCreateTime }}</view>
          </view>
        </view>
        <view class="px-40rpx p-t-30rpx" v-if="pageData.successTime">
          <view class="flex items-center justify-between">
            <view class="text-28rpx c-#333">付款时间</view>
            <view class="text-28rpx c-#333">{{ pageData.successTime }}</view>
          </view>
        </view>
        <view class="px-40rpx p-t-30rpx" v-if="pageData.finishTime">
          <view class="flex items-center justify-between">
            <view class="text-28rpx c-#333">完成时间</view>
            <view class="text-28rpx c-#333">{{ pageData.finishTime }}</view>
          </view>
        </view>
      </view>
    </view>
    <template #bottom>
      <view class="btn-fixed" v-if="!pageData.dealState && pageData.finishTimeD > 0">
        <view class="btn_box">
          <view class="btn_bg" @click="payOrder">
            确认支付￥{{ pageData.actualAmount / 100 }}元
          </view>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script setup lang="ts">
import { type ConfigProviderThemeVars } from 'wot-design-uni'
import { payDealDetail } from '@/service/order'
import { getTimeDifference } from '@/utils/util'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const list = {
  0: '未支付',
  1: '支付成功',
  2: '待退款',
  3: '部分退款',
  4: '全部退款',
  5: '已发货',
  6: '已完成',
  7: '已关闭',
}

const dealId = ref<number>(null)
const pageData = ref<any>({})
// 优惠金额/总金额
const discountRate = computed(() => {
  const actualAmount = Number(pageData.value.actualAmount) / 1000
  const totalAmount = Number(pageData.value.totalAmount) / 1000
  return (actualAmount / totalAmount).toFixed(2)
})
const queryList = async () => {
  const res = await payDealDetail({ dealId: Number(dealId.value) })
  if (res.code === 0) {
    res.data.dealStateName = list[res.data.dealState]
    if (!res.data.dealState) {
      res.data.finishTimeD = getTimeDifference(res.data.finishTime)
    }
    pageData.value = res.data
  }
  console.log(res, 'res=============')
}
// 去支付
const payOrder = () => {
  console.log('去支付')
}
function handleClickLeft() {
  uni.navigateBack()
}
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
  navbarColor: '#000',
}
onLoad(async (options) => {
  dealId.value = options.dealId
  await queryList()
})
</script>

<style lang="scss" scoped>
.btn-fixed {
  padding: 10rpx 80rpx;
  margin-bottom: 60rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx 0rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
:deep(.wd-count-down) {
  font-size: 34rpx !important;
  color: #ff4545 !important;
}
</style>
